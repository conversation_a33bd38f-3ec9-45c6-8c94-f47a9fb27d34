run:
  issues-exit-code: 1
  timeout: 3m
linters-settings:
  errcheck:
    check-type-assertions: true
  goconst:
    min-len: 2
    min-occurrences: 3
    gci:
    sections:
      - prefix(github.com/gosom/ledger)
    custom-order: true
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
  govet:
    shadow: true
  nolintlint:
    require-explanation: true
    require-specific: true
linters:
  disable-all: true
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - copyloopvar
    - exhaustive
    - goconst
    - gocritic
    - gofmt
    - goimports
    - gocyclo
    - gosec
    - gosimple
    - govet
    - ineffassign
    - misspell
    - nolintlint
    - nakedret
    - prealloc
    - predeclared
    - revive
    - staticcheck
    - stylecheck
    - testpackage
    - thelper
    - tparallel
    - typecheck
    - unconvert
    - unparam
    - whitespace
    - wsl

