# 🚀 Open Source Cold Email Automation Workflow

A completely free and open-source alternative to paid cold email automation tools, built with N8N, Ollama AI, and other FOSS technologies.

## 🎯 What This Workflow Does

1. **Lead Generation**: Scrapes Google Maps for business listings
2. **Data Enrichment**: Finds email addresses and company information
3. **AI Email Generation**: Creates personalized emails using local AI
4. **Email Delivery**: Sends emails via SMTP (your own mail server)
5. **Tracking & Management**: Tracks delivery status and manages follow-ups

## 🛠️ Tech Stack (100% Open Source)

| Component | Technology | Purpose | Cost |
|-----------|------------|---------|------|
| Workflow Engine | N8N | Automation orchestration | FREE |
| AI Model | Ollama (Llama 3.1) | Email content generation | FREE |
| Maps Scraper | Google Maps Scraper | Lead discovery | FREE |
| Email Finding | Hunter.io (Free Tier) | Email discovery | FREE (100/month) |
| Web Scraping | Playwright | Website data extraction | FREE |
| Data Storage | CSV Files | Lead management | FREE |
| Email Delivery | SMTP | Email sending | FREE (your server) |
| File Server | Nginx | Data file serving | FREE |

## 📋 Prerequisites

- Docker & Docker Compose
- SMTP server access (Gmail, SendGrid free tier, or your own)
- Basic technical knowledge

## 🚀 Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <your-repo>
   cd cold-email-automation
   chmod +x setup-opensource.sh
   ./setup-opensource.sh
   ```

2. **Import Workflow**:
   - Open N8N at http://localhost:5678
   - Import `workflow-cold-email-opensource.json`

3. **Configure Credentials**:
   - SMTP settings for email delivery
   - Hunter.io free API key
   - Any other API keys as needed

## 🔧 Configuration Guide

### 1. SMTP Configuration

In N8N, configure the SMTP node with:
- **Host**: Your SMTP server (e.g., smtp.gmail.com)
- **Port**: 587 (TLS) or 465 (SSL)
- **Username**: Your email address
- **Password**: Your email password/app password

### 2. Hunter.io Free API

1. Sign up at hunter.io (free tier: 100 searches/month)
2. Get your API key
3. Add to N8N credentials as `hunter_free`

### 3. Email Templates

The Ollama AI generates personalized emails based on:
- Company name and business type
- Location information
- Website content analysis
- Industry-specific messaging

## 📊 Workflow Steps Explained

### Part 1: Lead Generation
1. **Webhook Trigger**: Starts workflow with search query
2. **Google Maps Scraper**: Finds businesses matching criteria
3. **Batch Processing**: Splits results into manageable chunks
4. **Email Discovery**: Uses Hunter.io to find contact emails
5. **Web Scraping**: Extracts additional company information

### Part 2: Data Management
6. **CSV Storage**: Saves leads to local CSV file
7. **Duplicate Filtering**: Prevents processing same leads twice
8. **Data Enrichment**: Adds scraped website content

### Part 3: Email Automation
9. **Daily Cron**: Triggers email sending on schedule
10. **AI Generation**: Creates personalized email content
11. **SMTP Delivery**: Sends emails via your mail server
12. **Status Tracking**: Updates lead status in CSV

## 📈 Scaling & Optimization

### Free Tier Limits
- **Hunter.io**: 100 email searches/month
- **Ollama**: Unlimited local AI generation
- **SMTP**: Depends on your provider
- **Google Maps**: Rate-limited but free

### Performance Tips
- Use batch processing for large lead lists
- Implement delays between requests to avoid rate limits
- Monitor email deliverability metrics
- Regularly update your email templates

## 🔒 Privacy & Compliance

This setup runs entirely on your infrastructure:
- ✅ No data sent to third-party AI services
- ✅ Complete control over email content
- ✅ GDPR/CAN-SPAM compliant (with proper implementation)
- ✅ All data stored locally

## 🆚 Comparison with Paid Solutions

| Feature | This Setup | Paid SaaS | Savings |
|---------|------------|-----------|---------|
| Lead Generation | FREE | $50-200/month | $600-2400/year |
| AI Email Writing | FREE | $30-100/month | $360-1200/year |
| Email Delivery | FREE* | $25-100/month | $300-1200/year |
| Data Storage | FREE | $20-50/month | $240-600/year |
| **Total Annual Savings** | | | **$1500-5400** |

*Using your own SMTP server or free tier services

## 🔧 Customization Options

### Email Templates
Modify the Ollama prompt in the workflow to:
- Change tone and style
- Add industry-specific messaging
- Include different call-to-actions
- Adjust email length

### Data Sources
Extend the workflow to include:
- LinkedIn scraping
- Industry databases
- Social media profiles
- Company news and updates

### Follow-up Sequences
Add multiple email templates for:
- Initial outreach
- Follow-up emails
- Meeting requests
- Thank you messages

## 🐛 Troubleshooting

### Common Issues

1. **Ollama model not found**:
   ```bash
   docker exec mysetupn8n-ollama-1 ollama pull llama3.1:8b
   ```

2. **Email delivery fails**:
   - Check SMTP credentials
   - Verify port and security settings
   - Test with a simple email first

3. **Workflow errors**:
   - Check N8N logs in the interface
   - Verify all credentials are configured
   - Test each node individually

4. **CSV file not updating**:
   - Check file permissions in `/data` directory
   - Verify volume mounts in docker-compose.yml

## 📧 Support & Community

- Create issues for bugs or feature requests
- Fork and contribute improvements
- Share your success stories
- Help others in discussions

## 📜 License

This project is open source under the MIT License. Feel free to modify, distribute, and use commercially.

## 🚨 Legal Disclaimer

Please ensure compliance with:
- CAN-SPAM Act (US)
- GDPR (EU)
- Local email marketing laws
- Website terms of service

Always include unsubscribe links and respect opt-out requests.

---

**Happy automating! 🎉**
