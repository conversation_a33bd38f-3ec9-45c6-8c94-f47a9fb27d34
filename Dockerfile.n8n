FROM n8nio/n8n:latest

USER root

# Install system dependencies required for Playwright
RUN apk add --no-cache \
    python3 \
    py3-pip \
    py3-virtualenv \
    chromium \
    chromium-chromedriver \
    gcc \
    musl-dev \
    python3-dev \
    libffi-dev

# Create virtual environment and install packages
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip first
RUN pip install --upgrade pip

# Install Python packages in virtual environment
RUN pip install --no-cache-dir \
    requests \
    beautifulsoup4 \
    lxml \
    selenium

# Set environment variable for Chromium
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_PATH=/usr/bin/chromium-browser

# Create directory for data
RUN mkdir -p /data && chown -R node:node /data

# Copy scraper script
COPY scraper.py /app/scraper.py
RUN chmod +x /app/scraper.py

# Make virtual environment available to node user
RUN chown -R node:node /opt/venv

USER node
