<tr>
    <td>{{.ID}}</td>
    <td>{{.Name}}</td>
    <td>{{.Date}}</td>
    <td>
        <span class="status-indicator status-{{.Status}}">{{.Status}}</span>
    </td>
    <td>
        {{ if eq .Status "ok" }}
            <a href="/download?id={{.ID}}" download class="button download-button">Download</a>
        {{ end }}
        <button hx-delete="/delete?id={{.ID}}" 
                hx-target="closest tr"
                hx-swap="outerHTML"
                hx-confirm="Are you sure you want to delete this job?"
                class="delete-button">Delete</button>
    </td>
</tr>
