{"name": "Complete Open-Source Cold Email with Gmail Drafts", "nodes": [{"parameters": {"httpMethod": "POST", "path": "start-cold-email", "options": {"responseMode": "responseNode"}}, "id": "webhook-trigger", "name": "🚀 Start Cold Email Workflow", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [140, 300], "webhookId": "cold-email-start"}, {"parameters": {"jsCode": "// Process input and prepare for Google Maps scraping\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const data = item.json;\n  \n  // Validate required inputs\n  if (!data.business_query) {\n    throw new Error('business_query is required');\n  }\n  \n  const processedData = {\n    business_query: data.business_query,\n    results_count: data.results_count || 20,\n    email_method: data.email_method || 'google_maps_with_scraping',\n    target_location: data.target_location || '',\n    industry_filter: data.industry_filter || '',\n    approval_required: true, // Always require approval\n    workflow_id: `workflow_${Date.now()}`,\n    status: 'started',\n    timestamp: new Date().toISOString()\n  };\n  \n  results.push(processedData);\n}\n\nreturn results.map(item => ({ json: item }));"}, "id": "process-input", "name": "📝 Process Input & Validate", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [360, 300]}, {"parameters": {"command": "cd /app && echo \"{{ $json.business_query }}\" > /tmp/gmaps_query.txt && google-maps-scraper -input /tmp/gmaps_query.txt -c {{ $json.results_count }} -email -depth 3 -json", "additionalFields": {"workingDirectory": "/app"}}, "id": "google-maps-scraper", "name": "🗺️ Google Maps with Email Extraction", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [580, 300]}, {"parameters": {"jsCode": "// Parse Google Maps results and extract business data with emails\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const stdout = item.json.stdout;\n  \n  if (!stdout) {\n    console.log('No stdout from Google Maps scraper');\n    continue;\n  }\n  \n  // Parse JSON lines from Google Maps scraper output\n  const lines = stdout.split('\\n').filter(line => line.trim());\n  \n  for (const line of lines) {\n    try {\n      // Skip non-JSON lines (like headers)\n      if (!line.trim().startsWith('{')) continue;\n      \n      const business = JSON.parse(line);\n      \n      // Only process businesses with basic required data\n      if (!business.title) continue;\n      \n      const businessData = {\n        // Core business information\n        business_name: business.title || 'Unknown Business',\n        address: business.address || '',\n        phone: business.phone || '',\n        website: business.web_site || business.website || '',\n        rating: business.review_rating || 0,\n        review_count: business.review_count || 0,\n        \n        // Email information\n        emails_found: business.emails || [],\n        email_sources: business.emails ? ['google_maps_direct'] : [],\n        \n        // Additional business context\n        business_type: business.type || '',\n        hours: business.hours || '',\n        price_level: business.price_level || '',\n        \n        // Lead scoring\n        lead_score: 0,\n        lead_quality: 'Unknown',\n        \n        // Workflow tracking\n        needs_additional_email_search: (!business.emails || business.emails.length === 0),\n        workflow_status: 'scraped',\n        scraped_at: new Date().toISOString()\n      };\n      \n      // Calculate initial lead score\n      let score = 0;\n      if (businessData.emails_found.length > 0) score += 40;\n      if (businessData.website) score += 20;\n      if (businessData.phone) score += 15;\n      if (businessData.rating >= 4.0) score += 15;\n      if (businessData.review_count >= 50) score += 10;\n      \n      businessData.lead_score = score;\n      \n      // Determine lead quality\n      if (score >= 80) businessData.lead_quality = 'High';\n      else if (score >= 60) businessData.lead_quality = 'Medium';\n      else if (score >= 40) businessData.lead_quality = 'Low';\n      else businessData.lead_quality = 'Poor';\n      \n      results.push(businessData);\n      \n    } catch (e) {\n      console.log(`Error parsing line: ${line.substring(0, 100)}... Error: ${e.message}`);\n      continue;\n    }\n  }\n}\n\nconsole.log(`Processed ${results.length} businesses from Google Maps`);\nreturn results.map(item => ({ json: item }));"}, "id": "parse-gmaps-results", "name": "🔍 Parse Business Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-email-search", "leftValue": "={{ $json.needs_additional_email_search }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "check-needs-email-search", "name": "❓ Need Email Search?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1020, 300]}, {"parameters": {"jsCode": "// Enhanced email finding using web scraping and pattern generation\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const business = item.json;\n  \n  // Generate email patterns for businesses without found emails\n  const generateEmailPatterns = (businessName, website) => {\n    if (!website) return [];\n    \n    try {\n      const domain = new URL(website).hostname.replace('www.', '');\n      const patterns = [\n        `info@${domain}`,\n        `contact@${domain}`,\n        `hello@${domain}`,\n        `sales@${domain}`,\n        `support@${domain}`,\n        `admin@${domain}`,\n        `office@${domain}`\n      ];\n      \n      // Business name based patterns\n      const cleanName = businessName.toLowerCase()\n        .replace(/[^a-z0-9]/g, '')\n        .substring(0, 15);\n      \n      if (cleanName) {\n        patterns.push(`${cleanName}@${domain}`);\n      }\n      \n      return patterns;\n    } catch (e) {\n      return [];\n    }\n  };\n  \n  // Merge existing data with enhanced email information\n  const enhancedBusiness = {\n    ...business,\n    email_patterns: generateEmailPatterns(business.business_name, business.website),\n    total_email_candidates: (business.emails_found || []).length,\n    enhanced_search_complete: true,\n    enhanced_at: new Date().toISOString()\n  };\n  \n  // Update lead score based on email patterns\n  if (enhancedBusiness.email_patterns.length > 0) {\n    enhancedBusiness.lead_score += 10;\n    enhancedBusiness.email_sources.push('pattern_generation');\n  }\n  \n  // Recalculate lead quality\n  const score = enhancedBusiness.lead_score;\n  if (score >= 80) enhancedBusiness.lead_quality = 'High';\n  else if (score >= 60) enhancedBusiness.lead_quality = 'Medium';\n  else if (score >= 40) enhancedBusiness.lead_quality = 'Low';\n  else enhancedBusiness.lead_quality = 'Poor';\n  \n  results.push(enhancedBusiness);\n}\n\nreturn results.map(item => ({ json: item }));"}, "id": "pattern-email-generation", "name": "🔄 Generate Email <PERSON>s", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1240, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "ready-for-email", "leftValue": "={{ ($json.emails_found && $json.emails_found.length > 0) || ($json.email_patterns && $json.email_patterns.length > 0) }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "filter-ready-businesses", "name": "✅ Filter Ready Businesses", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1460, 300]}, {"parameters": {"model": "llama3.1:latest", "options": {"baseURL": "http://ollama:11434"}, "prompt": "=Generate a personalized cold email for the following business:\n\nBusiness: {{ $json.business_name }}\nWebsite: {{ $json.website }}\nPhone: {{ $json.phone }}\nAddress: {{ $json.address }}\nRating: {{ $json.rating }}/5 ({{ $json.review_count }} reviews)\nLead Score: {{ $json.lead_score }}\n\nEmail Contact Options:\n{% if $json.emails_found.length > 0 %}Found Emails: {{ $json.emails_found.join(', ') }}{% endif %}\n{% if $json.email_patterns.length > 0 %}Email Patterns: {{ $json.email_patterns.slice(0,3).join(', ') }}{% endif %}\n\nCreate a professional, personalized cold email that:\n1. Shows genuine research about their business\n2. Offers specific value relevant to their industry\n3. Has a clear, non-pushy call-to-action\n4. Keeps it concise (under 150 words)\n5. Uses a warm, professional tone\n\nFormat the response as:\nSUBJECT: [compelling subject line]\n\nDear [Business Name] Team,\n\n[Email body here]\n\nBest regards,\n[Your Name]\n[Your Title]\n[Your Company]"}, "id": "generate-email-content", "name": "🤖 Generate Email with <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1680, 300]}, {"parameters": {"jsCode": "// Parse Ollama-generated email and prepare for Gmail draft creation\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const business = item.json;\n  // Ollama response is in response property\n  const aiContent = business.response || business.message?.content || '';\n  \n  // Parse subject and body from AI response\n  let emailSubject = '';\n  let emailBody = '';\n  \n  try {\n    // Extract subject line\n    const subjectMatch = aiContent.match(/SUBJECT:\\s*(.+?)\\n/i);\n    if (subjectMatch) {\n      emailSubject = subjectMatch[1].trim();\n    } else {\n      emailSubject = `Partnership Opportunity - ${business.business_name}`;\n    }\n    \n    // Extract email body (everything after Dear until Best regards)\n    const bodyMatch = aiContent.match(/Dear[\\s\\S]*?(?=Best regards|Sincerely|Kind regards)/i);\n    if (bodyMatch) {\n      emailBody = bodyMatch[0].trim();\n    } else {\n      // Fallback: use entire content\n      emailBody = aiContent;\n    }\n    \n  } catch (e) {\n    console.log(`Error parsing Ollama content: ${e.message}`);\n    emailSubject = `Partnership Opportunity - ${business.business_name}`;\n    emailBody = aiContent;\n  }\n  \n  // Determine recipient email (prioritize found emails over patterns)\n  let recipientEmail = '';\n  if (business.emails_found && business.emails_found.length > 0) {\n    recipientEmail = business.emails_found[0]; // Use first found email\n  } else if (business.email_patterns && business.email_patterns.length > 0) {\n    recipientEmail = business.email_patterns[0]; // Use first pattern\n  }\n  \n  // Create draft data\n  const draftData = {\n    // Business information\n    business_name: business.business_name,\n    business_address: business.address,\n    business_phone: business.phone,\n    business_website: business.website,\n    business_rating: business.rating,\n    lead_score: business.lead_score,\n    lead_quality: business.lead_quality,\n    \n    // Email information\n    recipient_email: recipientEmail,\n    email_subject: emailSubject,\n    email_body: emailBody,\n    email_sources: business.email_sources || [],\n    \n    // Gmail draft specifics\n    draft_status: 'pending_approval',\n    requires_review: true,\n    \n    // Metadata\n    generated_at: new Date().toISOString(),\n    workflow_id: business.workflow_id || 'unknown',\n    ai_model: 'ollama-llama3.1'\n  };\n  \n  results.push(draftData);\n}\n\nconsole.log(`Prepared ${results.length} email drafts for approval using Ollama`);\nreturn results.map(item => ({ json: item }));"}, "id": "prepare-draft-data", "name": "📝 Prepare Draft Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1900, 300]}, {"parameters": {"operation": "draft", "message": {"to": "={{ $json.recipient_email }}", "subject": "={{ $json.email_subject }}", "emailType": "text", "message": "={{ $json.email_body }}"}, "additionalFields": {"ccList": "", "bccList": ""}}, "id": "create-gmail-draft", "name": "📧 Create Gmail Draft", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [2120, 300]}, {"parameters": {"operation": "appendOrUpdate", "documentId": "1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a", "sheetName": "Email Drafts for Review", "columns": {"mappingMode": "defineBelow", "value": {"Timestamp": "={{ $json.generated_at }}", "Business Name": "={{ $json.business_name }}", "Recipient Email": "={{ $json.recipient_email }}", "Email Subject": "={{ $json.email_subject }}", "Lead Score": "={{ $json.lead_score }}", "Lead Quality": "={{ $json.lead_quality }}", "Business Website": "={{ $json.business_website }}", "Business Phone": "={{ $json.business_phone }}", "Draft Status": "={{ $json.draft_status }}", "Gmail Draft ID": "={{ $json.id }}", "Review Required": "YES", "Approved": "PENDING", "Email Sources": "={{ $json.email_sources.join(', ') }}"}}, "options": {}}, "id": "save-drafts-to-sheets", "name": "📊 Save to Review Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [2340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Cold email workflow completed successfully\",\n  \"results\": {\n    \"total_businesses_processed\": {{ $input.all().length }},\n    \"gmail_drafts_created\": {{ $input.all().filter(item => item.json.id).length }},\n    \"high_quality_leads\": {{ $input.all().filter(item => item.json.lead_quality === 'High').length }},\n    \"medium_quality_leads\": {{ $input.all().filter(item => item.json.lead_quality === 'Medium').length }},\n    \"workflow_type\": \"open_source_with_gmail_drafts\",\n    \"approval_required\": true\n  },\n  \"next_steps\": [\n    \"Review Gmail drafts at https://mail.google.com/mail/u/0/#drafts\",\n    \"Approve emails in Google Sheets\",\n    \"Send approved emails manually or set up approval automation\"\n  ],\n  \"links\": {\n    \"gmail_drafts\": \"https://mail.google.com/mail/u/0/#drafts\",\n    \"review_sheet\": \"https://docs.google.com/spreadsheets/d/1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a\"\n  },\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"workflow_version\": \"v3.0-opensource-gmail-drafts\"\n}"}, "id": "webhook-response", "name": "✅ Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2560, 300]}], "connections": {"🚀 Start Cold Email Workflow": {"main": [[{"node": "📝 Process Input & Validate", "type": "main", "index": 0}]]}, "📝 Process Input & Validate": {"main": [[{"node": "🗺️ Google Maps with Email Extraction", "type": "main", "index": 0}]]}, "🗺️ Google Maps with Email Extraction": {"main": [[{"node": "🔍 Parse Business Results", "type": "main", "index": 0}]]}, "🔍 Parse Business Results": {"main": [[{"node": "❓ Need Email Search?", "type": "main", "index": 0}]]}, "❓ Need Email Search?": {"main": [[{"node": "🔄 Generate Email <PERSON>s", "type": "main", "index": 0}], [{"node": "🔄 Generate Email <PERSON>s", "type": "main", "index": 0}]]}, "🔄 Generate Email Patterns": {"main": [[{"node": "✅ Filter Ready Businesses", "type": "main", "index": 0}]]}, "✅ Filter Ready Businesses": {"main": [[{"node": "🤖 Generate Email with <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "🤖 Generate Email with Ollama": {"main": [[{"node": "📝 Prepare Draft Data", "type": "main", "index": 0}]]}, "📝 Prepare Draft Data": {"main": [[{"node": "📧 Create Gmail Draft", "type": "main", "index": 0}]]}, "📧 Create Gmail Draft": {"main": [[{"node": "📊 Save to Review Sheet", "type": "main", "index": 0}]]}, "📊 Save to Review Sheet": {"main": [[{"node": "✅ Success Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": ["cold-email", "open-source", "gmail-drafts", "approval-workflow"], "triggerCount": 0, "updatedAt": "2025-08-08T10:00:00.000Z", "versionId": "v3.0-complete-opensource"}